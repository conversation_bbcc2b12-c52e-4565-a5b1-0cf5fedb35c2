{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Notebook 演示\n", "\n", "这是一个 Jupyter Notebook 的完整示例，展示基本功能和用法。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 基础示例\n", "print('Hello, Notebook!')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 数据操作示例\n", "\n", "下面展示一些基本的数据操作:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "\n", "# 创建示例数据\n", "data = {\n", "    '姓名': ['张三', '李四', '王五'],\n", "    '年龄': [25, 30, 35],\n", "    '城市': ['北京', '上海', '广州']\n", "}\n", "\n", "df = pd.DataFrame(data)\n", "df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 可视化示例\n", "\n", "使用 matplotlib 进行简单可视化:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "# 示例数据\n", "x = np.linspace(0, 10, 100)\n", "y = np.sin(x)\n", "\n", "# 绘制图形\n", "plt.figure(figsize=(8, 4))\n", "plt.plot(x, y, label='sin(x)')\n", "plt.title('正弦函数示例')\n", "plt.xlabel('x')\n", "plt.ylabel('y')\n", "plt.legend()\n", "plt.grid()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 交互式控件\n", "\n", "Jupyter Notebook 支持交互式控件:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from ipywidgets import interact\n", "\n", "def square(x):\n", "    return x * x\n", "\n", "interact(square, x=(0, 10, 1));"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 4}