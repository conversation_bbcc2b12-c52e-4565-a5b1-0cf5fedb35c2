{"cells": [{"cell_type": "code", "execution_count": null, "id": "200ebfc9", "metadata": {}, "outputs": [], "source": ["import anthropic\n", "\n", "client = anthropic.Anthropic(api_key=\"************************************************************************************************************\")"]}, {"cell_type": "code", "execution_count": 10, "id": "6320be6c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["BetaMessage(id='msg_01H48d1twQKfZ1vr3QCYqTXn', container=None, content=[BetaTextBlock(citations=None, text=\"I'll help you save a temporary file with 'hello jonny' to your desktop. First, let me check the desktop directory structure and then create the file.\", type='text'), BetaToolUseBlock(id='toolu_01GHCvNfEEXZYSHohcXzC653', input={'command': 'ls -la ~/Desktop'}, name='bash', type='tool_use')], model='claude-sonnet-4-20250514', role='assistant', stop_reason='tool_use', stop_sequence=None, type='message', usage=BetaUsage(cache_creation=None, cache_creation_input_tokens=0, cache_read_input_tokens=0, input_tokens=2621, output_tokens=90, server_tool_use=None, service_tier='standard'))\n"]}], "source": ["response = client.beta.messages.create(\n", "    model=\"claude-sonnet-4-20250514\",  # or another compatible model\n", "    max_tokens=1024,\n", "    tools=[\n", "        {\n", "          \"type\": \"computer_20250124\",\n", "          \"name\": \"computer\",\n", "          \"display_width_px\": 1024,\n", "          \"display_height_px\": 768,\n", "          \"display_number\": 1,\n", "        },\n", "        {\n", "          \"type\": \"text_editor_20250124\",\n", "          \"name\": \"str_replace_editor\"\n", "        },\n", "        {\n", "          \"type\": \"bash_20250124\",\n", "          \"name\": \"bash\"\n", "        }\n", "    ],\n", "    messages=[{\"role\": \"user\", \"content\": \"Save a tmp file with 'hello jonny' to my desktop.(do not take a screenshot to see the current desktop)\"}],\n", "    betas=[\"computer-use-2025-01-24\"]\n", ")\n", "print(response)\n"]}, {"cell_type": "markdown", "id": "15c67dd8", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}